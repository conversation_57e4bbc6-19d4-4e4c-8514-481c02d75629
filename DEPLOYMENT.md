# 🚀 Deployment Guide for Strategy.co

## Option 1: Render.com (Recommended - FREE)

### Step 1: Prepare Your Code
✅ Already done! Your app is ready for deployment.

### Step 2: Push to GitHub
1. Create a new repository on GitHub
2. In your project folder, run:
```bash
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPO_NAME.git
git push -u origin main
```

### Step 3: Deploy on Render
1. Go to [render.com](https://render.com) and sign up
2. Click "New +" → "Web Service"
3. Connect your GitHub repository
4. Render will auto-detect it's a Python app
5. Use these settings:
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `gunicorn app:app`
   - **Environment**: Python 3
6. Click "Deploy"

🎉 Your app will be live at: `https://your-app-name.onrender.com`

---

## Option 2: Railway.app (Also Easy)

### Step 1: Install Railway CLI
```bash
npm install -g @railway/cli
```

### Step 2: Deploy
```bash
railway login
railway deploy
```

---

## Option 3: Heroku (Paid but Reliable)

### Step 1: Install Heroku CLI
Download from: https://devcenter.heroku.com/articles/heroku-cli

### Step 2: Deploy
```bash
heroku login
heroku create your-app-name
git push heroku main
```

---

## 🔒 Security Notes

Before sharing publicly, consider:

1. **Change the secret key** in `app.py`:
```python
app.secret_key = "your-super-secret-production-key-here"
```

2. **Use environment variables** for sensitive data:
```python
app.secret_key = os.environ.get('SECRET_KEY', 'fallback-key')
```

3. **Add user management** if needed for broader access

---

## 📱 Sharing Your App

Once deployed, you can share the URL with users:
- `https://your-app-name.onrender.com` (Render)
- `https://your-app-name.up.railway.app` (Railway)
- `https://your-app-name.herokuapp.com` (Heroku)

Users will need login credentials from your `users.csv` file.

---

## 🛠 Files Added for Deployment

- `requirements.txt` - Python dependencies
- `render.yaml` - Render configuration
- Updated `app.py` - Production settings

Your app is now deployment-ready! 🎉
