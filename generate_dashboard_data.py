#!/usr/bin/env python3
"""
Script to generate JSON files for dashboard consumption with insight metrics
"""

import json
import os
import statistics

from data_processor import CoreValuesDataProcessor, create_sample_data


def calculate_insight_metrics(processor):
    """Calculate all insight metrics displayed on the dashboard"""

    # Get basic data
    team_stats = processor.calculate_team_statistics()
    team_averages = processor.calculate_team_averages()
    comparison = processor.get_manager_vs_team_comparison()

    insights = {}

    # 1. Highest Rating (Nejvyšší hodnocení)
    highest_value = max(team_averages, key=team_averages.get)
    highest_rating = team_averages[highest_value]
    insights["highest_rating"] = {
        "core_value": highest_value,
        "rating": round(highest_rating, 1),
        "description": f'Hodnota "{highest_value}" dosahuje nejvyššího průměrného hodnocení napříč všemi respondenty.',
    }

    # 2. Team Consensus (Konsensus týmu) - lowest standard deviation
    lowest_stdev_value = min(team_stats, key=lambda x: team_stats[x]["stdev"])
    lowest_stdev = team_stats[lowest_stdev_value]["stdev"]
    insights["team_consensus"] = {
        "core_value": lowest_stdev_value,
        "stdev": round(lowest_stdev, 1),
        "description": f'Nejvyšší shoda v hodnocení je u hodnoty "{lowest_stdev_value}" s nejnižší směrodatnou odchylkou.',
    }

    # 3. Growth Potential (Potenciál růstu) - highest range
    highest_range_value = max(team_stats, key=lambda x: team_stats[x]["range"])
    highest_range = team_stats[highest_range_value]["range"]
    insights["growth_potential"] = {
        "core_value": highest_range_value,
        "range": highest_range,
        "description": f'Hodnota "{highest_range_value}" má největší potenciál pro zlepšení s nejširším rozpětím hodnocení.',
    }

    # 4. Manager Perspective (Manažerský pohled) - average difference
    if comparison:
        total_diff = sum(comp["difference"] for comp in comparison.values())
        avg_diff = total_diff / len(comparison)
        manager_trend = "výše" if avg_diff > 0 else "níže"
        insights["manager_perspective"] = {
            "average_difference": round(avg_diff, 1),
            "trend": manager_trend,
            "description": f"Manažer hodnotí všechny hodnoty konzistentně {manager_trend} než průměr týmu.",
        }
    else:
        insights["manager_perspective"] = {
            "average_difference": 0.0,
            "trend": "neutrální",
            "description": "Manažerská data nejsou k dispozici.",
        }

    # Additional insights

    # 5. Most Consistent Value (Nejkonzistentnější hodnota)
    most_consistent = min(team_stats, key=lambda x: team_stats[x]["stdev"])
    insights["most_consistent"] = {
        "core_value": most_consistent,
        "stdev": round(team_stats[most_consistent]["stdev"], 2),
        "mean": round(team_stats[most_consistent]["mean"], 1),
    }

    # 6. Most Variable Value (Nejvariabilnější hodnota)
    most_variable = max(team_stats, key=lambda x: team_stats[x]["stdev"])
    insights["most_variable"] = {
        "core_value": most_variable,
        "stdev": round(team_stats[most_variable]["stdev"], 2),
        "mean": round(team_stats[most_variable]["mean"], 1),
    }

    # 7. Overall Team Performance
    overall_avg = statistics.mean(team_averages.values())
    insights["overall_performance"] = {
        "average_rating": round(overall_avg, 1),
        "total_responses": len(processor.team_ratings),
        "core_values_count": len(processor.core_values),
    }

    # 8. Manager vs Team Analysis
    if comparison:
        manager_higher_count = sum(
            1 for comp in comparison.values() if comp["difference"] > 0
        )
        team_higher_count = sum(
            1 for comp in comparison.values() if comp["difference"] < 0
        )
        equal_count = sum(1 for comp in comparison.values() if comp["difference"] == 0)

        insights["manager_team_analysis"] = {
            "manager_higher": manager_higher_count,
            "team_higher": team_higher_count,
            "equal": equal_count,
            "total_values": len(comparison),
        }

    # 9. Rating Distribution Analysis
    all_ratings = []
    for member in processor.team_ratings:
        all_ratings.extend(member.ratings.values())

    rating_distribution = {i: all_ratings.count(i) for i in range(1, 6)}
    most_common_rating = max(rating_distribution, key=rating_distribution.get)

    insights["rating_distribution"] = {
        "distribution": rating_distribution,
        "most_common_rating": most_common_rating,
        "total_ratings": len(all_ratings),
        "percentage_distribution": {
            rating: round((count / len(all_ratings)) * 100, 1)
            for rating, count in rating_distribution.items()
        },
    }

    # 10. Team Engagement Score
    # Based on variance from neutral (3) - higher variance indicates more engaged responses
    engagement_scores = []
    for member in processor.team_ratings:
        member_variance = statistics.variance(
            [abs(rating - 3) for rating in member.ratings.values()]
        )
        engagement_scores.append(member_variance)

    avg_engagement = statistics.mean(engagement_scores) if engagement_scores else 0
    insights["team_engagement"] = {
        "average_engagement_score": round(avg_engagement, 2),
        "interpretation": (
            "vysoká"
            if avg_engagement > 1.5
            else "střední" if avg_engagement > 0.8 else "nízká"
        ),
        "description": f'Úroveň angažovanosti týmu při hodnocení je {insights["team_engagement"]["interpretation"] if "team_engagement" in insights else "střední"}.',
    }

    # 11. Improvement Priority Index
    # Combines low average with high variability to suggest focus areas
    priority_scores = {}
    for value in processor.core_values:
        avg_rating = team_averages[value]
        variability = team_stats[value]["stdev"]
        # Lower average + higher variability = higher priority
        priority_score = (5 - avg_rating) + variability
        priority_scores[value] = round(priority_score, 2)

    top_priority = max(priority_scores, key=priority_scores.get)
    insights["improvement_priority"] = {
        "priority_scores": priority_scores,
        "top_priority": top_priority,
        "priority_score": priority_scores[top_priority],
        "description": f'Hodnota "{top_priority}" má nejvyšší prioritu pro zlepšení na základě kombinace nízkého hodnocení a vysoké variability.',
    }

    # 12. Team Cohesion Index
    # Measures how similar team members' rating patterns are
    if len(processor.team_ratings) > 1:
        cohesion_scores = []
        team_members_data = [
            list(member.ratings.values()) for member in processor.team_ratings
        ]

        # Calculate pairwise correlations
        for i in range(len(team_members_data)):
            for j in range(i + 1, len(team_members_data)):
                try:
                    correlation = statistics.correlation(
                        team_members_data[i], team_members_data[j]
                    )
                    cohesion_scores.append(correlation)
                except statistics.StatisticsError:
                    # Handle case where correlation can't be calculated
                    continue

        avg_cohesion = statistics.mean(cohesion_scores) if cohesion_scores else 0
        insights["team_cohesion"] = {
            "cohesion_index": round(avg_cohesion, 2),
            "interpretation": (
                "vysoká"
                if avg_cohesion > 0.7
                else "střední" if avg_cohesion > 0.3 else "nízká"
            ),
            "description": f'Soudržnost týmu v hodnocení hodnot je {insights["team_cohesion"]["interpretation"] if "team_cohesion" in insights else "střední"}.',
        }
    else:
        insights["team_cohesion"] = {
            "cohesion_index": 0.0,
            "interpretation": "nedostupná",
            "description": "Soudržnost týmu nelze vypočítat s méně než 2 respondenty.",
        }

    return insights


def main():
    """Generate dashboard data files with insight metrics"""
    print("🚀 Generating dashboard data with insight metrics...")

    # Create sample data
    processor = create_sample_data()

    # Calculate insight metrics
    insights = calculate_insight_metrics(processor)

    # Save dashboard data
    files = processor.save_dashboard_data("dashboard_data")

    # Save insights separately
    insights_file = os.path.join("dashboard_data", "insights_metrics.json")
    with open(insights_file, "w", encoding="utf-8") as f:
        json.dump(insights, f, indent=2, ensure_ascii=False)
    files["insights_metrics"] = insights_file

    print("\n✅ Successfully created dashboard files:")
    for file_type, filepath in files.items():
        file_size = os.path.getsize(filepath)
        print(f"  📄 {file_type}: {filepath} ({file_size} bytes)")

    # Show detailed insights
    print("\n🔍 Calculated Insight Metrics:")

    print(f"\n🏆 Highest Rating:")
    hr = insights["highest_rating"]
    print(f"  - Value: {hr['core_value']}")
    print(f"  - Rating: {hr['rating']}/5")
    print(f"  - Description: {hr['description']}")

    print(f"\n🤝 Team Consensus:")
    tc = insights["team_consensus"]
    print(f"  - Value: {tc['core_value']}")
    print(f"  - Standard Deviation: {tc['stdev']}")
    print(f"  - Description: {tc['description']}")

    print(f"\n📈 Growth Potential:")
    gp = insights["growth_potential"]
    print(f"  - Value: {gp['core_value']}")
    print(f"  - Range: {gp['range']}")
    print(f"  - Description: {gp['description']}")

    print(f"\n👔 Manager Perspective:")
    mp = insights["manager_perspective"]
    print(f"  - Average Difference: {mp['average_difference']}")
    print(f"  - Trend: {mp['trend']}")
    print(f"  - Description: {mp['description']}")

    print(f"\n📊 Overall Performance:")
    op = insights["overall_performance"]
    print(f"  - Average Rating: {op['average_rating']}/5")
    print(f"  - Total Responses: {op['total_responses']}")
    print(f"  - Core Values: {op['core_values_count']}")

    if "manager_team_analysis" in insights:
        print(f"\n⚖️ Manager vs Team Analysis:")
        mta = insights["manager_team_analysis"]
        print(
            f"  - Manager rates higher: {mta['manager_higher']}/{mta['total_values']} values"
        )
        print(
            f"  - Team rates higher: {mta['team_higher']}/{mta['total_values']} values"
        )
        print(f"  - Equal ratings: {mta['equal']}/{mta['total_values']} values")

    # Display new enhanced metrics
    print(f"\n📊 Rating Distribution:")
    rd = insights["rating_distribution"]
    print(f"  - Most common rating: {rd['most_common_rating']}/5")
    print(f"  - Distribution: {rd['percentage_distribution']}")

    print(f"\n🎯 Team Engagement:")
    te = insights["team_engagement"]
    print(f"  - Engagement level: {te['interpretation']}")
    print(f"  - Score: {te['average_engagement_score']}")
    print(f"  - Description: {te['description']}")

    print(f"\n🎯 Improvement Priority:")
    ip = insights["improvement_priority"]
    print(f"  - Top priority: {ip['top_priority']}")
    print(f"  - Priority score: {ip['priority_score']}")
    print(f"  - Description: {ip['description']}")

    print(f"\n🤝 Team Cohesion:")
    tc_cohesion = insights["team_cohesion"]
    print(f"  - Cohesion level: {tc_cohesion['interpretation']}")
    print(f"  - Index: {tc_cohesion['cohesion_index']}")
    print(f"  - Description: {tc_cohesion['description']}")

    # Show sample content from key files
    print("\n📊 Sample content from key files:")

    # Visualization data
    with open(files["visualization_data"], "r", encoding="utf-8") as f:
        viz_data = json.load(f)
    print(f"\n🎯 Visualization Data:")
    print(f"  - Core values: {viz_data['core_values']}")
    print(f"  - Team members: {viz_data['team_members']}")
    print(f"  - Manager: {viz_data['manager_name']}")

    # Team statistics
    with open(files["team_statistics"], "r", encoding="utf-8") as f:
        stats_data = json.load(f)
    print(f"\n📈 Team Statistics:")
    for value, stats in stats_data["statistics"].items():
        print(
            f"  - {value}: Mean={stats['mean']}, StdDev={stats['stdev']}, Range={stats['range']}"
        )

    print(f"\n🎉 Dashboard data with insights ready!")
    print(f"📁 Files saved in 'dashboard_data' directory.")
    print(f"� Use 'insights_metrics.json' for dashboard insight cards.")


if __name__ == "__main__":
    main()
