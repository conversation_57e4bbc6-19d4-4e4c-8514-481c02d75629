import csv
import json

from flask import Flask, jsonify, redirect, render_template, request, session, url_for

from data_processor import CoreValuesDataProcessor, create_sample_data
from generate_dashboard_data import calculate_insight_metrics

app = Flask(__name__)
app.secret_key = "your_secret_key"  # Replace with a strong secret


def load_users():
    users = {}
    with open("users.csv", newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            users[row["email"]] = row["code"]
    return users


@app.route("/", methods=["GET"])
def login():
    return render_template("modern-login.html")


@app.route("/login", methods=["POST"])
def login_ajax():
    try:
        email = request.form["email"]
        password = request.form["password"]
        users = load_users()

        if email in users and users[email] == password:
            session["user"] = email
            return jsonify(
                {"success": True, "message": "<PERSON><PERSON><PERSON>š<PERSON><PERSON> přihlášen!", "redirect": "/index"}
            )
        else:
            return jsonify(
                {"success": False, "message": "Neplatný email nebo unikátní kód."}
            )
    except Exception as e:
        return jsonify({"success": False, "message": "Chyba při přihlašování."})


@app.route("/index")
def index():
    if "user" not in session:
        return redirect(url_for("login"))
    return render_template("modern-index.html")


@app.route("/visuals")
def visuals():
    if "user" not in session:
        return redirect(url_for("login"))

    # Create sample data for demonstration
    # In production, this would load from your database
    processor = create_sample_data()
    viz_data = processor.export_for_visualization()

    # Calculate insights for dynamic cards
    insights_data = calculate_insight_metrics(processor)

    return render_template(
        "modern-visuals.html",
        viz_data=json.dumps(viz_data, ensure_ascii=False),
        insights_data=json.dumps(insights_data, ensure_ascii=False),
    )


@app.route("/api/visuals-data")
def api_visuals_data():
    """API endpoint to get visualization data"""
    if "user" not in session:
        return jsonify({"error": "Unauthorized"}), 401

    # Create sample data for demonstration
    # In production, this would load from your database
    processor = create_sample_data()
    viz_data = processor.export_for_visualization()
    insights_data = calculate_insight_metrics(processor)

    return jsonify({"visualization": viz_data, "insights": insights_data})


@app.route("/contact", methods=["POST"])
def contact():
    if "user" not in session:
        return redirect(url_for("login"))
    # Here you would process the contact form
    # For now, we'll just redirect back to the index page
    return redirect(url_for("index"))


@app.route("/logout")
def logout():
    session.clear()
    return redirect(url_for("login"))


if __name__ == "__main__":
    app.run(debug=True)
