// Modern JavaScript with Micro-interactions

document.addEventListener('DOMContentLoaded', function() {
    // Navigation functionality
    initNavigation();

    // Smooth scrolling
    initSmoothScrolling();

    // Scroll animations
    initScrollAnimations();

    // Counter animations
    initCounterAnimations();

    // Floating cards animation
    initFloatingCards();

    // Parallax effects
    initParallaxEffects();

    // Intersection Observer for animations
    initIntersectionObserver();

    // Questions carousel
    initQuestionsCarousel();
});

// Navigation functionality
function initNavigation() {
    const navbar = document.getElementById('navbar');
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Navbar scroll effect
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Mobile menu toggle
    navToggle.addEventListener('click', () => {
        navMenu.classList.toggle('active');

        // Animate hamburger bars
        const bars = navToggle.querySelectorAll('.bar');
        bars.forEach((bar, index) => {
            bar.style.transform = navMenu.classList.contains('active')
                ? `rotate(${index === 0 ? 45 : index === 1 ? 0 : -45}deg) translate(${index === 1 ? '100px' : '0'}, ${index === 0 ? '6px' : index === 2 ? '-6px' : '0'})`
                : 'none';
            bar.style.opacity = navMenu.classList.contains('active') && index === 1 ? '0' : '1';
        });
    });

    // Active link highlighting
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');

            // Close mobile menu
            navMenu.classList.remove('active');

            // Reset hamburger
            const bars = navToggle.querySelectorAll('.bar');
            bars.forEach(bar => {
                bar.style.transform = 'none';
                bar.style.opacity = '1';
            });
        });
    });
}

// Custom smooth scrolling with easing
function smoothScrollTo(targetPosition, duration = 1200) {
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    // Easing function for smoother animation
    function easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }

    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);
        const ease = easeInOutCubic(progress);

        window.scrollTo(0, startPosition + distance * ease);

        if (timeElapsed < duration) {
            requestAnimationFrame(animation);
        }
    }

    requestAnimationFrame(animation);
}

// Smooth scrolling functionality
function initSmoothScrolling() {
    const scrollButtons = document.querySelectorAll('[data-scroll]');
    const scrollIndicators = document.querySelectorAll('.scroll-indicator');

    // Handle all elements with data-scroll attribute
    scrollButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = button.getAttribute('data-scroll');
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 70; // Account for fixed navbar
                smoothScrollTo(offsetTop, 1200); // 1.2 seconds duration

                // Add click animation
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            }
        });
    });

    // Handle scroll indicators specifically
    scrollIndicators.forEach(indicator => {
        indicator.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = indicator.getAttribute('data-scroll');
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 70;
                smoothScrollTo(offsetTop, 1200);

                // Add bounce animation to the scroll arrow
                const scrollArrow = indicator.querySelector('.scroll-arrow');
                if (scrollArrow) {
                    scrollArrow.style.transform = 'scale(0.9) translateY(5px)';
                    setTimeout(() => {
                        scrollArrow.style.transform = '';
                    }, 200);
                }
            }
        });
    });

    // Navigation link smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 70;
                smoothScrollTo(offsetTop, 1200);
            }
        });
    });
}

// Scroll-triggered animations
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('[data-aos]');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animationType = element.getAttribute('data-aos');
                const delay = element.getAttribute('data-aos-delay') || 0;

                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                    element.classList.add('aos-animate');
                }, delay);

                observer.unobserve(element);
            }
        });
    }, observerOptions);

    animatedElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });
}

// Counter animations
function initCounterAnimations() {
    const counters = document.querySelectorAll('[data-count]');

    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-count'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;

        const updateCounter = () => {
            current += increment;
            if (current < target) {
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };

        updateCounter();
    };

    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// Floating cards animation
function initFloatingCards() {
    const cards = document.querySelectorAll('.floating-cards .card');

    cards.forEach((card, index) => {
        // Add random floating animation delays
        const delay = Math.random() * 2;
        card.style.animationDelay = `${delay}s`;

        // Add hover micro-interactions
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-15px) scale(1.05) rotate(2deg)';
            card.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.2)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = '';
            card.style.boxShadow = '';
        });

        // Add click animation
        card.addEventListener('click', () => {
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = '';
            }, 150);
        });
    });
}

// Parallax effects
function initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.hero-visual, .floating-cards');

    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;

        parallaxElements.forEach(element => {
            element.style.transform = `translateY(${rate}px)`;
        });
    });
}

// Intersection Observer for various animations
function initIntersectionObserver() {
    // Service cards animation
    const serviceCards = document.querySelectorAll('.service-card.modern');

    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    }, { threshold: 0.1 });

    serviceCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        cardObserver.observe(card);
    });

    // Team members animation
    const teamMembers = document.querySelectorAll('.team-member.modern');

    const teamObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) scale(1)';
                }, index * 150);
            }
        });
    }, { threshold: 0.1 });

    teamMembers.forEach(member => {
        member.style.opacity = '0';
        member.style.transform = 'translateY(50px) scale(0.9)';
        member.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        teamObserver.observe(member);
    });
}

// Button micro-interactions
document.querySelectorAll('.btn').forEach(button => {
    button.addEventListener('mouseenter', () => {
        button.style.transform = 'translateY(-2px)';
    });

    button.addEventListener('mouseleave', () => {
        button.style.transform = '';
    });

    button.addEventListener('mousedown', () => {
        button.style.transform = 'translateY(0) scale(0.98)';
    });

    button.addEventListener('mouseup', () => {
        button.style.transform = 'translateY(-2px)';
    });
});

// Add loading animation
window.addEventListener('load', () => {
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Cursor trail effect (optional)
function initCursorTrail() {
    const cursor = document.createElement('div');
    cursor.className = 'cursor-trail';
    cursor.style.cssText = `
        position: fixed;
        width: 20px;
        height: 20px;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        opacity: 0.7;
        transition: transform 0.1s ease;
    `;
    document.body.appendChild(cursor);

    document.addEventListener('mousemove', (e) => {
        cursor.style.left = e.clientX - 10 + 'px';
        cursor.style.top = e.clientY - 10 + 'px';
    });

    document.addEventListener('mousedown', () => {
        cursor.style.transform = 'scale(1.5)';
    });

    document.addEventListener('mouseup', () => {
        cursor.style.transform = 'scale(1)';
    });
}

// Uncomment to enable cursor trail
// initCursorTrail();

// Questions Carousel Functionality
function initQuestionsCarousel() {
    const carousel = document.getElementById('questionCarousel');
    if (!carousel) return;

    // Strategy-related questions data
    const questions = [
        {
            id: 1,
            text: "Jaký je nejdůležitější faktor při vytváření dlouhodobé strategie?",
            options: [
                "Analýza konkurence a tržních trendů",
                "Pochopení potřeb a očekávání zákazníků",
                "Finanční plánování a rozpočet",
                "Interní kapacity a zdroje organizace"
            ]
        },
        {
            id: 2,
            text: "Jak často by měla organizace revidovat svou strategii?",
            options: [
                "Jednou ročně během strategického plánování",
                "Každé čtvrtletí na základě výsledků",
                "Průběžně podle změn v prostředí",
                "Pouze při významných změnách na trhu"
            ]
        },
        {
            id: 3,
            text: "Co je klíčové pro úspěšnou implementaci strategie?",
            options: [
                "Jasná komunikace cílů všem zaměstnancům",
                "Dostatečné finanční zdroje",
                "Silné vedení a podpora managementu",
                "Flexibilní organizační struktura"
            ]
        },
        {
            id: 4,
            text: "Jaký přístup je nejefektivnější při řízení změn?",
            options: [
                "Top-down přístup s jasným vedením",
                "Bottom-up přístup s participací zaměstnanců",
                "Kombinace obou přístupů",
                "Postupná implementace po malých krocích"
            ]
        },
        {
            id: 5,
            text: "Co je nejdůležitější při měření úspěchu strategie?",
            options: [
                "Finanční ukazatele (ROI, zisk, tržby)",
                "Operační metriky (efektivita, kvalita)",
                "Zákaznické metriky (spokojenost, loajalita)",
                "Kombinace všech typů ukazatelů"
            ]
        },
        {
            id: 6,
            text: "Jak by měla organizace reagovat na neočekávané změny?",
            options: [
                "Rychle adaptovat strategii podle situace",
                "Držet se původního plánu a vyčkat",
                "Konzultovat s externími poradci",
                "Provést důkladnou analýzu před změnou"
            ]
        },
        {
            id: 7,
            text: "Jaká je role inovací ve strategickém plánování?",
            options: [
                "Inovace jsou klíčové pro konkurenceschopnost",
                "Inovace jsou rizikové a měly by být omezené",
                "Inovace jsou důležité pouze v tech sektorech",
                "Inovace by měly být postupné a kontrolované"
            ]
        },
        {
            id: 8,
            text: "Co je nejdůležitější při budování strategických partnerství?",
            options: [
                "Vzájemná důvěra a transparentnost",
                "Jasně definované smlouvy a podmínky",
                "Komplementární schopnosti a zdroje",
                "Dlouhodobá vize a společné cíle"
            ]
        },
        {
            id: 9,
            text: "Jak by měla organizace přistupovat k digitální transformaci?",
            options: [
                "Postupně implementovat nové technologie",
                "Provést komplexní změnu najednou",
                "Zaměřit se pouze na zákaznické rozhraní",
                "Začít s interními procesy a systémy"
            ]
        },
        {
            id: 10,
            text: "Co je klíčové pro udržení konkurenční výhody?",
            options: [
                "Kontinuální inovace a zlepšování",
                "Nízké náklady a efektivita",
                "Silná značka a zákaznická loajalita",
                "Unikátní produkty nebo služby"
            ]
        }
    ];

    let currentQuestionIndex = 0;
    let selectedAnswers = {};
    let startTime = Date.now();
    let questionStartTimes = {};

    // Initialize carousel
    function initCarousel() {
        renderQuestions();
        updateControls();
        updateProgress();
        setupEventListeners();
        startTimer();
    }

    // Start timing
    function startTimer() {
        startTime = Date.now();
        questionStartTimes[0] = startTime;
    }

    // Render all questions
    function renderQuestions() {
        carousel.innerHTML = '';

        questions.forEach((question, index) => {
            const questionCard = createQuestionCard(question, index);
            carousel.appendChild(questionCard);
        });

        // Show first question
        showQuestion(0);
    }

    // Create individual question card
    function createQuestionCard(question, index) {
        const card = document.createElement('div');
        card.className = 'question-card';
        card.setAttribute('data-question', index);

        card.innerHTML = `
            <div class="question-number">${question.id}</div>
            <h3 class="question-text">${question.text}</h3>
            <div class="question-options">
                ${question.options.map((option, optionIndex) => `
                    <button class="option-button" data-option="${optionIndex}">
                        ${option}
                    </button>
                `).join('')}
            </div>
        `;

        return card;
    }

    // Show specific question
    function showQuestion(index) {
        const cards = carousel.querySelectorAll('.question-card');

        cards.forEach((card, cardIndex) => {
            card.classList.remove('active', 'prev', 'next');

            if (cardIndex === index) {
                card.classList.add('active');
            } else if (cardIndex < index) {
                card.classList.add('prev');
            } else {
                card.classList.add('next');
            }
        });

        currentQuestionIndex = index;
        updateControls();
        updateProgress();
        updateIndicators();

        // Track question start time
        questionStartTimes[index] = Date.now();
    }

    // Update navigation controls
    function updateControls() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (prevBtn) {
            prevBtn.disabled = currentQuestionIndex === 0;
        }

        if (nextBtn) {
            nextBtn.disabled = currentQuestionIndex === questions.length - 1;
        }
    }

    // Update progress bar and text
    function updateProgress() {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        if (progressBar) {
            const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
            progressBar.style.setProperty('--progress-width', `${progress}%`);
        }

        if (progressText) {
            progressText.textContent = `${currentQuestionIndex + 1} / ${questions.length}`;
        }
    }

    // Update indicators
    function updateIndicators() {
        const indicatorsContainer = document.getElementById('carouselIndicators');
        if (!indicatorsContainer) return;

        // Create indicators if they don't exist
        if (indicatorsContainer.children.length === 0) {
            questions.forEach((_, index) => {
                const indicator = document.createElement('div');
                indicator.className = 'indicator-dot';
                indicator.setAttribute('data-question', index);
                indicator.addEventListener('click', () => showQuestion(index));
                indicatorsContainer.appendChild(indicator);
            });
        }

        // Update active indicator
        const indicators = indicatorsContainer.querySelectorAll('.indicator-dot');
        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === currentQuestionIndex);
        });
    }

    // Setup event listeners
    function setupEventListeners() {
        // Navigation buttons
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                if (currentQuestionIndex > 0) {
                    showQuestion(currentQuestionIndex - 1);
                }
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                if (currentQuestionIndex < questions.length - 1) {
                    showQuestion(currentQuestionIndex + 1);
                }
            });
        }

        // Option selection
        carousel.addEventListener('click', (e) => {
            if (e.target.classList.contains('option-button')) {
                handleOptionSelection(e.target);
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (document.getElementById('questions').getBoundingClientRect().top < window.innerHeight &&
                document.getElementById('questions').getBoundingClientRect().bottom > 0) {

                if (e.key === 'ArrowLeft' && currentQuestionIndex > 0) {
                    showQuestion(currentQuestionIndex - 1);
                } else if (e.key === 'ArrowRight' && currentQuestionIndex < questions.length - 1) {
                    showQuestion(currentQuestionIndex + 1);
                }
            }
        });
    }

    // Handle option selection
    function handleOptionSelection(optionButton) {
        const questionCard = optionButton.closest('.question-card');
        const questionIndex = parseInt(questionCard.getAttribute('data-question'));
        const optionIndex = parseInt(optionButton.getAttribute('data-option'));

        // Remove previous selection
        questionCard.querySelectorAll('.option-button').forEach(btn => {
            btn.classList.remove('selected');
        });

        // Add selection to clicked option
        optionButton.classList.add('selected');

        // Store answer
        selectedAnswers[questionIndex] = optionIndex;

        // Add selection animation
        optionButton.style.transform = 'scale(1.05)';
        setTimeout(() => {
            optionButton.style.transform = '';
        }, 200);

        // Auto-advance after a short delay
        setTimeout(() => {
            if (currentQuestionIndex < questions.length - 1) {
                showQuestion(currentQuestionIndex + 1);
            } else {
                // Show completion message or results
                showCompletionMessage();
            }
        }, 1000);
    }

    // Show completion message
    function showCompletionMessage() {
        const endTime = Date.now();
        const totalTime = Math.round((endTime - startTime) / 1000 / 60 * 10) / 10; // minutes with 1 decimal
        const completedCount = Object.keys(selectedAnswers).length;
        const completionRate = Math.round((completedCount / questions.length) * 100);

        const completionCard = document.createElement('div');
        completionCard.className = 'question-card active';
        completionCard.innerHTML = `
            <div class="question-number">✓</div>
            <h3 class="question-text">Děkujeme za vyplnění dotazníku!</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--space-lg); margin: var(--space-xl) 0; max-width: 500px;">
                <div style="text-align: center; padding: var(--space-lg); background: var(--background-secondary); border-radius: var(--radius-lg);">
                    <div style="font-size: 2rem; font-weight: var(--font-weight-bold); color: var(--primary-color);">${completedCount}</div>
                    <div style="font-size: 0.9rem; color: var(--text-secondary);">z ${questions.length} otázek</div>
                </div>
                <div style="text-align: center; padding: var(--space-lg); background: var(--background-secondary); border-radius: var(--radius-lg);">
                    <div style="font-size: 2rem; font-weight: var(--font-weight-bold); color: var(--accent-color);">${totalTime}</div>
                    <div style="font-size: 0.9rem; color: var(--text-secondary);">minut</div>
                </div>
                <div style="text-align: center; padding: var(--space-lg); background: var(--background-secondary); border-radius: var(--radius-lg);">
                    <div style="font-size: 2rem; font-weight: var(--font-weight-bold); color: var(--highlight-color);">${completionRate}%</div>
                    <div style="font-size: 0.9rem; color: var(--text-secondary);">dokončeno</div>
                </div>
            </div>
            <p style="color: var(--text-secondary); font-size: 1.1rem; margin-bottom: var(--space-xl);">
                Vaše odpovědi pomohou zlepšit naše služby a strategické poradenství.
            </p>
            <div style="display: flex; gap: var(--space-md); justify-content: center; flex-wrap: wrap;">
                <button class="btn btn-primary" onclick="location.reload()">
                    <span>Začít znovu</span>
                    <i class="fas fa-redo"></i>
                </button>
                <button class="btn btn-secondary" onclick="window.location.href='/index'">
                    <span>Zpět na hlavní stránku</span>
                    <i class="fas fa-home"></i>
                </button>
            </div>
        `;

        // Hide current question and show completion
        const currentCard = carousel.querySelector('.question-card.active');
        if (currentCard) {
            currentCard.classList.remove('active');
            currentCard.classList.add('prev');
        }

        carousel.appendChild(completionCard);

        // Update progress to 100%
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            progressBar.style.setProperty('--progress-width', '100%');
        }

        const progressText = document.getElementById('progressText');
        if (progressText) {
            progressText.textContent = 'Dokončeno';
        }

        // Show statistics if on questions page
        showQuestionStatistics(completedCount, totalTime, completionRate);
    }

    // Show question statistics
    function showQuestionStatistics(completed, time, rate) {
        const statsSection = document.getElementById('questionStats');
        if (statsSection) {
            // Update statistics
            const completedElement = document.getElementById('completedCount');
            const timeElement = document.getElementById('timeSpent');
            const scoreElement = document.getElementById('strategicScore');

            if (completedElement) completedElement.textContent = completed;
            if (timeElement) timeElement.textContent = time;
            if (scoreElement) {
                let score = 'Začátečník';
                if (rate >= 90 && time <= 5) score = 'Expert';
                else if (rate >= 80 && time <= 8) score = 'Pokročilý';
                else if (rate >= 70) score = 'Středně pokročilý';
                scoreElement.textContent = score;
            }

            // Show stats with animation
            statsSection.style.display = 'block';
            statsSection.style.opacity = '0';
            statsSection.style.transform = 'translateY(30px)';

            setTimeout(() => {
                statsSection.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                statsSection.style.opacity = '1';
                statsSection.style.transform = 'translateY(0)';
            }, 500);
        }
    }

    // Initialize the carousel
    initCarousel();
}
