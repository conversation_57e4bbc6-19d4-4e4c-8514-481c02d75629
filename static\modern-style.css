/* Modern Flat Design with Pantone Colors */
:root {
  /* Pantone-inspired Color Palette */
  --pantone-blue: #0F4C75;        /* Classic Blue */
  --pantone-coral: #FF6B6B;       /* Living Coral */
  --pantone-mint: #4ECDC4;        /* Mint */
  --pantone-lavender: #A8E6CF;    /* Lavender */
  --pantone-yellow: #FFD93D;      /* Illuminating Yellow */
  --pantone-purple: #6C5CE7;      /* Purple */
  --pantone-orange: #FD79A8;      /* Orange */

  /* Main Color System */
  --primary-color: var(--pantone-coral);
  --secondary-color: var(--pantone-blue);
  --accent-color: var(--pantone-mint);
  --highlight-color: var(--pantone-yellow);
  --text-primary: #2D3436;
  --text-secondary: #636E72;
  --text-light: #B2BEC3;
  --background-primary: #FFFFFF;
  --background-secondary: #F8F9FA;
  --background-dark: #2D3436;

  /* Modern Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Spacing System */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;

  /* Shadows (Modern Flat) */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

.modern-body {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-regular);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-primary);
  overflow-x: hidden;
}

/* Modern Navigation */
.modern-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: var(--transition-normal);
}

.modern-nav.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--secondary-color);
}

.brand-link {
  text-decoration: none;
  transition: var(--transition-normal);
}

.brand-link:hover {
  transform: translateY(-1px);
}

.brand-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: var(--transition-normal);
}

.brand-link:hover .brand-text {
  background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
}

.nav-link {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-size: 0.95rem;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
  transform: translateY(-1px);
}

.nav-link.active {
  color: var(--primary-color);
  background: rgba(255, 107, 107, 0.1);
}

.logout-btn {
  background: var(--primary-color);
  color: white !important;
  border-radius: var(--radius-full);
  padding: var(--space-sm) var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  transition: var(--transition-bounce);
}

.logout-btn:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.bar {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

/* Fullscreen Hero Section */
.hero-fullscreen {
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-primary) 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="%23f1f3f4" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 107, 107, 0.05), rgba(15, 76, 117, 0.05));
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-text {
  animation: fadeInUp 1s ease-out;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: var(--font-weight-extrabold);
  line-height: 1.1;
  margin-bottom: var(--space-xl);
  color: var(--text-primary);
}

.title-line {
  display: block;
  animation: slideInLeft 0.8s ease-out;
}

.title-line:nth-child(2) {
  animation-delay: 0.2s;
}

.title-line:nth-child(3) {
  animation-delay: 0.4s;
}

.highlight {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-2xl);
  line-height: 1.6;
  animation: fadeInUp 1s ease-out 0.3s both;
}

.hero-actions {
  display: flex;
  gap: var(--space-lg);
  animation: fadeInUp 1s ease-out 0.6s both;
}

/* Modern Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg) var(--space-2xl);
  border: none;
  border-radius: var(--radius-full);
  font-family: var(--font-primary);
  font-weight: var(--font-weight-semibold);
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--text-light);
}

.btn-secondary:hover {
  background: var(--text-primary);
  color: white;
  border-color: var(--text-primary);
  transform: translateY(-2px);
}

/* Floating Cards Animation */
.hero-visual {
  position: relative;
  height: 500px;
  animation: fadeInRight 1s ease-out 0.4s both;
}

.floating-cards {
  position: relative;
  width: 100%;
  height: 100%;
}

.card {
  position: absolute;
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-md);
  transition: var(--transition-normal);
  animation: float 6s ease-in-out infinite;
}

.card i {
  font-size: 2rem;
  color: var(--primary-color);
}

.card span {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-1 {
  top: 10%;
  left: 10%;
  background: linear-gradient(135deg, var(--pantone-coral), var(--pantone-orange));
  color: white;
  animation-delay: 0s;
}

.card-1 i, .card-1 span {
  color: white;
}

.card-2 {
  top: 20%;
  right: 20%;
  background: linear-gradient(135deg, var(--pantone-mint), var(--pantone-lavender));
  animation-delay: 1.5s;
}

.card-3 {
  bottom: 30%;
  left: 20%;
  background: linear-gradient(135deg, var(--pantone-yellow), var(--pantone-orange));
  animation-delay: 3s;
}

.card-4 {
  bottom: 10%;
  right: 10%;
  background: linear-gradient(135deg, var(--pantone-purple), var(--pantone-blue));
  color: white;
  animation-delay: 4.5s;
}

.card-4 i, .card-4 span {
  color: white;
}

.card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: var(--shadow-xl);
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: var(--space-2xl);
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: var(--transition-normal);
}

.scroll-arrow:hover {
  background: var(--secondary-color);
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: var(--space-4xl);
}

.section-tag {
  display: inline-block;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--space-lg);
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  line-height: 1.2;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Services Section */
.services-modern {
  padding: var(--space-4xl) 0;
  background: var(--background-secondary);
  position: relative;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-2xl);
}

.service-card.modern {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.service-card.modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  transform: scaleX(0);
  transform-origin: left;
  transition: var(--transition-normal);
}

.service-card.modern:hover::before {
  transform: scaleX(1);
}

.service-card.modern:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.service-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-lg);
  transition: var(--transition-bounce);
}

.service-icon i {
  font-size: 1.5rem;
  color: white;
}

.service-card.modern:hover .service-icon {
  transform: scale(1.1) rotate(5deg);
}

.service-card.modern h3 {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.service-card.modern p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-lg);
}

.service-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
}

.feature {
  background: var(--background-secondary);
  color: var(--text-secondary);
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
}

/* Stats Section */
.stats-section {
  padding: var(--space-4xl) 0;
  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
  color: white;
  position: relative;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-2xl);
}

.stat-item {
  text-align: center;
  padding: var(--space-xl);
}

.stat-number {
  font-size: 3rem;
  font-weight: var(--font-weight-extrabold);
  color: var(--highlight-color);
  margin-bottom: var(--space-sm);
  display: block;
}

.stat-label {
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  opacity: 0.9;
}

/* Team Section */
.team-modern {
  padding: var(--space-4xl) 0;
  background: white;
  position: relative;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-2xl);
}

.team-member.modern {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.team-member.modern:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.member-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.team-member.modern:hover .member-image img {
  transform: scale(1.1);
}

.member-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.team-member.modern:hover .member-overlay {
  opacity: 0.9;
}

.member-social {
  display: flex;
  gap: var(--space-md);
  transform: translateY(20px);
  transition: var(--transition-normal);
}

.team-member.modern:hover .member-social {
  transform: translateY(0);
}

.social-link {
  width: 40px;
  height: 40px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-bounce);
}

.social-link:hover {
  transform: scale(1.2);
  background: var(--highlight-color);
  color: var(--secondary-color);
}

.member-info {
  padding: var(--space-2xl);
}

.member-info h3 {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.member-role {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  font-size: 0.95rem;
  margin-bottom: var(--space-md);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.member-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-lg);
}

.member-skills {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
}

.skill {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
}

/* Contact Section */
.contact-modern {
  padding: var(--space-4xl) 0;
  background: var(--background-secondary);
  position: relative;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  margin-top: var(--space-3xl);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-2xl);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
  padding: var(--space-xl);
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.contact-details h3 {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.contact-details p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.contact-form-wrapper {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-form.modern {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.form-group {
  position: relative;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--space-lg);
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-lg);
  font-family: var(--font-primary);
  font-size: 1rem;
  background: var(--background-primary);
  transition: var(--transition-normal);
  outline: none;
  resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.form-group label {
  position: absolute;
  top: var(--space-lg);
  left: var(--space-lg);
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-normal);
  pointer-events: none;
  background: var(--background-primary);
  padding: 0 var(--space-sm);
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group select:focus + label,
.form-group select:not([value=""]) + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label {
  top: -8px;
  left: var(--space-md);
  font-size: 0.875rem;
  color: var(--primary-color);
}

.form-group select {
  cursor: pointer;
}

.form-group textarea {
  min-height: 120px;
  font-family: var(--font-primary);
}

.form-submit-wrapper {
  display: flex;
  justify-content: center;
  margin-top: var(--space-md);
}

/* Scroll Up Indicator */
.scroll-up {
  bottom: var(--space-2xl);
}

.scroll-up .scroll-arrow {
  background: var(--secondary-color);
  animation: bounceUp 2s infinite;
}

.scroll-up .scroll-arrow:hover {
  background: var(--primary-color);
  transform: scale(1.1);
}

@keyframes bounceUp {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(10px);
  }
  60% {
    transform: translateX(-50%) translateY(5px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: white;
    flex-direction: column;
    padding: var(--space-xl);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-toggle {
    display: flex;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
    text-align: center;
  }

  .hero-visual {
    height: 300px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .services-grid,
  .team-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .contact-item {
    padding: var(--space-lg);
  }

  .contact-form-wrapper {
    padding: var(--space-xl);
  }
}

/* Visuals Page Styles */
.visuals-hero {
  background: linear-gradient(135deg, var(--pantone-blue) 0%, var(--pantone-coral) 100%);
  position: relative;
}

.visuals-section {
  padding: var(--space-4xl) 0;
  background: var(--background-secondary);
  position: relative;
}

.visualization-container {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-lg);
  margin-top: var(--space-2xl);
}

.chart-controls {
  display: flex;
  gap: var(--space-xl);
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-lg);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  min-width: 200px;
}

.control-group label {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.speed-indicator {
  font-size: 0.8rem;
  color: var(--text-light);
  text-align: center;
  margin-top: var(--space-xs);
}

.modern-select {
  padding: var(--space-sm) var(--space-md);
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-md);
  background: white;
  font-family: var(--font-primary);
  font-size: 0.9rem;
  transition: var(--transition-normal);
  cursor: pointer;
}

.modern-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.modern-slider {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  outline: none;
  transition: var(--transition-normal);
}

.modern-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  transition: var(--transition-normal);
}

.modern-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 8px rgba(255, 107, 107, 0.1);
}

.chart-wrapper {
  position: relative;
  height: 500px;
  margin: var(--space-xl) 0;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: var(--space-xl);
  margin-top: var(--space-lg);
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.insights-section {
  padding: var(--space-4xl) 0;
  background: white;
  position: relative;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-xl);
  margin-top: var(--space-2xl);
}

.insight-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: var(--transition-normal);
  text-align: center;
}

.insight-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.insight-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--pantone-coral), var(--pantone-orange));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-lg);
}

.insight-icon i {
  font-size: 1.5rem;
  color: white;
}

.insight-card h3 {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
}

.insight-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-lg);
}

.insight-metric {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.metric-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.metric-label {
  font-size: 0.85rem;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive adjustments for visuals */
@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: var(--space-lg);
  }

  .control-group {
    min-width: auto;
  }

  .chart-wrapper {
    height: 400px;
  }

  .chart-legend {
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }
}

/* Dark mode support for visuals */
@media (prefers-color-scheme: dark) {
  .visualization-container,
  .insight-card {
    background: rgba(45, 52, 54, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .modern-select {
    background: var(--background-secondary);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
  }

  .visuals-section {
    background: var(--background-dark);
  }

  .insights-section {
    background: var(--background-secondary);
  }
}

/* Questions Section */
.questions-modern {
  padding: var(--space-4xl) 0;
  background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-primary) 100%);
  position: relative;
}

.questions-carousel-container {
  margin-top: var(--space-3xl);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.carousel-wrapper {
  position: relative;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.question-carousel {
  position: relative;
  height: 500px;
  overflow: hidden;
}

.question-card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: var(--space-3xl);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  background: white;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.6s ease;
  transform: translateX(100%);
  opacity: 0;
}

.question-card.active {
  transform: translateX(0);
  opacity: 1;
}

.question-card.prev {
  transform: translateX(-100%);
  opacity: 0;
}

.question-card.next {
  transform: translateX(100%);
  opacity: 0;
}

.question-number {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: 1.25rem;
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-md);
}

.question-text {
  font-size: 1.5rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2xl);
  line-height: 1.4;
  max-width: 600px;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  width: 100%;
  max-width: 500px;
}

.option-button {
  padding: var(--space-lg) var(--space-xl);
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-lg);
  background: white;
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-normal);
  text-align: left;
  position: relative;
  overflow: hidden;
}

.option-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 107, 0.1), transparent);
  transition: var(--transition-slow);
}

.option-button:hover {
  border-color: var(--primary-color);
  transform: translateX(8px);
  box-shadow: var(--shadow-md);
}

.option-button:hover::before {
  left: 100%;
}

.option-button.selected {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border-color: var(--primary-color);
  transform: scale(1.02);
  box-shadow: var(--shadow-lg);
}

.carousel-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-xl) var(--space-2xl);
  background: var(--background-secondary);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.carousel-btn {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  transition: var(--transition-bounce);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

.carousel-btn:hover {
  background: var(--secondary-color);
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.carousel-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.carousel-indicators {
  display: flex;
  gap: var(--space-sm);
  align-items: center;
}

.indicator-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: var(--transition-normal);
}

.indicator-dot.active {
  background: var(--primary-color);
  transform: scale(1.3);
}

.indicator-dot:hover {
  background: var(--accent-color);
  transform: scale(1.2);
}

.carousel-progress {
  padding: var(--space-lg) var(--space-2xl);
  background: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  margin-right: var(--space-lg);
  overflow: hidden;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 3px;
  transition: width 0.6s ease;
  width: var(--progress-width, 10%);
}

.progress-text {
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  font-size: 0.9rem;
  min-width: 60px;
  text-align: right;
}

/* Question animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Responsive design for questions */
@media (max-width: 768px) {
  .questions-carousel-container {
    margin-left: var(--space-lg);
    margin-right: var(--space-lg);
  }

  .question-card {
    padding: var(--space-2xl) var(--space-lg);
    height: auto;
    min-height: 450px;
  }

  .question-text {
    font-size: 1.25rem;
    margin-bottom: var(--space-xl);
  }

  .question-options {
    max-width: 100%;
  }

  .option-button {
    padding: var(--space-md) var(--space-lg);
    font-size: 0.95rem;
  }

  .option-button:hover {
    transform: translateY(-2px);
  }

  .carousel-controls {
    padding: var(--space-lg);
  }

  .carousel-btn {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }

  .carousel-progress {
    padding: var(--space-md) var(--space-lg);
  }

  .progress-text {
    font-size: 0.85rem;
    min-width: 50px;
  }
}
